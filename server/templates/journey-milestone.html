<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{milestoneTitle}} - Your Sacred Journey</title>
  <style>
    body {
      font-family: 'Georgia', 'Times New Roman', serif;
      line-height: 1.7;
      color: #2c3e50;
      max-width: 650px;
      margin: 0 auto;
      padding: 0;
      background-color: #f8f9fa;
    }
    
    .email-container {
      background-color: #ffffff;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      margin: 20px;
    }
    
    .header {
      text-align: center;
      padding: 40px 30px;
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
      color: #2c3e50;
      position: relative;
    }
    
    .header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 300;
      letter-spacing: 1px;
    }
    
    .milestone-badge {
      display: inline-block;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 8px 20px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      margin-top: 10px;
      letter-spacing: 0.5px;
    }
    
    .content {
      padding: 40px 30px;
    }
    
    .greeting {
      font-size: 18px;
      margin-bottom: 25px;
      color: #667eea;
    }
    
    .healing-name {
      font-weight: bold;
      font-style: italic;
      color: #764ba2;
    }
    
    .milestone-content {
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-radius: 8px;
      padding: 30px;
      margin: 30px 0;
    }
    
    .reflection-prompt {
      background-color: #fff;
      border-left: 4px solid #ff9a9e;
      padding: 20px;
      margin: 25px 0;
      border-radius: 0 8px 8px 0;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }
    
    .reflection-prompt h4 {
      margin: 0 0 15px 0;
      color: #2c3e50;
      font-size: 18px;
    }
    
    .reflection-questions {
      list-style: none;
      padding: 0;
      margin: 15px 0;
    }
    
    .reflection-questions li {
      padding: 8px 0;
      position: relative;
      padding-left: 25px;
      color: #5a6c7d;
    }
    
    .reflection-questions li::before {
      content: '🌸';
      position: absolute;
      left: 0;
      top: 8px;
    }
    
    .cta-section {
      text-align: center;
      margin: 40px 0;
      padding: 30px;
      background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
      border-radius: 8px;
    }
    
    .cta-button {
      display: inline-block;
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
      color: #2c3e50;
      text-decoration: none;
      padding: 15px 30px;
      border-radius: 25px;
      font-size: 16px;
      font-weight: 500;
      margin: 15px 10px;
      transition: transform 0.2s ease;
      box-shadow: 0 4px 15px rgba(255, 154, 158, 0.3);
    }
    
    .cta-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(255, 154, 158, 0.4);
    }
    
    .cta-button.secondary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .wisdom-quote {
      font-style: italic;
      text-align: center;
      font-size: 16px;
      color: #5a6c7d;
      margin: 30px 0;
      padding: 20px;
      border-left: 4px solid #ff9a9e;
      background-color: #f8f9fa;
      border-radius: 0 8px 8px 0;
    }
    
    .progress-indicator {
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      margin: 25px 0;
      text-align: center;
    }
    
    .progress-bar {
      background-color: #e9ecef;
      border-radius: 10px;
      height: 8px;
      margin: 15px 0;
      overflow: hidden;
    }
    
    .progress-fill {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      height: 100%;
      border-radius: 10px;
      transition: width 0.3s ease;
    }
    
    .footer {
      background-color: #2c3e50;
      color: #ecf0f1;
      text-align: center;
      padding: 30px;
      font-size: 14px;
      line-height: 1.6;
    }
    
    .footer a {
      color: #ff9a9e;
      text-decoration: none;
    }
    
    @media only screen and (max-width: 600px) {
      .email-container {
        margin: 10px;
        border-radius: 8px;
      }
      
      .header {
        padding: 30px 20px;
      }
      
      .header h1 {
        font-size: 24px;
      }
      
      .content {
        padding: 30px 20px;
      }
      
      .cta-button {
        display: block;
        margin: 10px 0;
        padding: 12px 25px;
        font-size: 15px;
      }
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="header">
      <h1>{{milestoneTitle}}</h1>
      <div class="milestone-badge">{{milestoneDay}}</div>
    </div>
    
    <div class="content">
      <p class="greeting">Beloved <span class="healing-name">{{healingName}}</span>,</p>
      
      <p>
        {{milestoneGreeting}}
      </p>
      
      <div class="progress-indicator">
        <h4>Your Journey Progress</h4>
        <div class="progress-bar">
          <div class="progress-fill" style="width: {{progressPercentage}}%;"></div>
        </div>
        <p><strong>{{progressPercentage}}% Complete</strong> • {{daysInJourney}} days of transformation</p>
      </div>
      
      <div class="milestone-content">
        <h3>{{milestoneTitle}} Reflection</h3>
        <p>{{milestoneDescription}}</p>
        
        <div class="reflection-prompt">
          <h4>Sacred Reflection Questions</h4>
          <ul class="reflection-questions">
            {{#each reflectionQuestions}}
            <li>{{this}}</li>
            {{/each}}
          </ul>
        </div>
      </div>
      
      <div class="wisdom-quote">
        "{{wisdomQuote}}" — {{quoteAuthor}}
      </div>
      
      <p>
        {{encouragementMessage}}
      </p>
      
      <div class="cta-section">
        <p><strong>Ready to reflect on your journey?</strong></p>
        <p>Take a moment to honor your progress and insights.</p>
        
        <a href="{{reflectionUrl}}" class="cta-button">
          🌸 Share Your Reflection 🌸
        </a>
        
        <a href="{{chatbotUrl}}" class="cta-button secondary">
          💬 Talk to Your Companion
        </a>
      </div>
      
      <p>
        Remember, dear soul, that every step on this journey is sacred. Your willingness to look within, 
        to question, to grow, and to heal is a gift not only to yourself but to all those whose lives you touch.
      </p>
      
      <div class="wisdom-quote">
        "The privilege of a lifetime is to become who you truly are." — Carl Jung
      </div>
      
      <p style="margin-top: 30px;">
        With infinite love and deep respect for your journey,<br>
        <strong>The Sacred Healing Team</strong>
      </p>
    </div>
    
    <div class="footer">
      <p>
        This milestone email is part of your personalized Sacred Healing Journey.
      </p>
      
      <p>
        Need support or have questions? We're here for you at 
        <a href="mailto:{{supportEmail}}">{{supportEmail}}</a>
      </p>
      
      <p>
        To update your preferences or unsubscribe, 
        <a href="{{unsubscribeUrl}}">click here</a>.
      </p>
      
      <p style="margin-top: 20px; font-size: 12px; opacity: 0.8;">
        Sacred Healing Companion & Journey Hub<br>
        Honoring your unique path of transformation
      </p>
    </div>
  </div>
</body>
</html>
