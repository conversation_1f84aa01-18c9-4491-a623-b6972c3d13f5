{"name": "sacred-healing-hub", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server/index.js", "dev:server": "nodemon server/index.js", "start": "concurrently \"npm run dev\" \"npm run dev:server\"", "test": "jest --passWithNoTests", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:server": "jest --config=jest.server.config.js", "test:client": "jest --testPathPattern=src/", "test:coverage:frontend": "jest --testPathPattern=src/ --coverage --coverageDirectory=coverage/frontend", "test:coverage:backend": "jest --testPathPattern=server/ --coverage --coverageDirectory=coverage/backend", "test:coverage:combined": "npm run test:coverage:frontend && npm run test:coverage:backend && node scripts/combine-coverage.js", "cypress:open": "cypress open", "cypress:run": "cypress run", "cypress:run:chatbot": "cypress run --spec \"cypress/e2e/chatbot*.cy.js\"", "cypress:run:reflection": "cypress run --spec \"cypress/e2e/reflection*.cy.js\"", "cypress:run:auth": "cypress run --spec \"cypress/e2e/authentication.cy.js\"", "cypress:run:navigation": "cypress run --spec \"cypress/e2e/navigation.cy.js\"", "test:e2e": "start-server-and-test start http://localhost:3000 cypress:run", "test:e2e:open": "start-server-and-test start http://localhost:3000 cypress:open", "migrate:dry-run": "node server/scripts/migrateToGoogleSheets.js --dry-run", "migrate:all": "node server/scripts/migrateToGoogleSheets.js", "migrate:users": "node server/scripts/migrateToGoogleSheets.js --table=users", "migrate:reflections": "node server/scripts/migrateToGoogleSheets.js --table=reflections", "migrate:conversations": "node server/scripts/migrateToGoogleSheets.js --table=conversations", "migrate:feedback": "node server/scripts/migrateToGoogleSheets.js --table=feedback"}, "dependencies": {"@sentry/react": "^7.101.1", "@sentry/tracing": "^7.101.1", "airtable": "^0.12.2", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto-js": "^4.2.0", "csurf": "^1.10.0", "dotenv": "^16.3.1", "express": "^4.18.2", "framer-motion": "^11.0.8", "googleapis": "^149.0.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.344.0", "mailchimp-api-v3": "^1.15.0", "openai": "^4.28.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.22.1", "react-toastify": "^10.0.4", "supertest": "^6.3.3", "uuid": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "babel-jest": "^29.7.0", "concurrently": "^8.2.2", "cypress": "^14.3.2", "eslint": "^9.9.1", "eslint-plugin-cypress": "^2.15.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "nodemon": "^3.0.3", "postcss": "^8.4.35", "start-server-and-test": "^2.0.3", "tailwindcss": "^3.4.1", "ts-jest": "^29.1.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}